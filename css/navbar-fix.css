/* Simple and Reliable Navigation Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(220, 38, 38, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Theme-aware header background */
[data-theme="dark"] .header {
    background: rgba(31, 41, 55, 0.98) !important;
    border-bottom: 1px solid rgba(220, 38, 38, 0.2) !important;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 24px;
    padding-right: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-red);
    text-decoration: none;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.02);
}

.nav-menu {
    display: flex;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 0;
    align-items: center;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    display: block;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 1rem;
    padding: 0.875rem 1.25rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 0.125rem;
    white-space: nowrap;
}

.nav-menu a:hover,
.nav-menu a.active {
    background: var(--primary-red);
    color: white;
    transform: translateY(-1px);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.language-switcher,
.theme-toggle {
    position: relative;
}

.lang-btn,
.theme-btn {
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    padding: 0.625rem 0.875rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-dark);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.875rem;
    min-width: auto;
    white-space: nowrap;
}

.lang-btn:hover, 
.theme-btn:hover {
    background: var(--primary-red);
    color: white;
    border-color: var(--primary-red);
    transform: translateY(-1px);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-dark);
    cursor: pointer;
    padding: 0.5rem;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .header-content {
        padding: 0.875rem 0;
        flex-wrap: nowrap;
        position: relative;
    }

    .logo {
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid var(--gray-200);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        z-index: 999;
        width: 100%;
        order: 3;
        max-height: calc(100vh - 80px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Dark theme support for mobile menu */
    [data-theme="dark"] .nav-menu {
        background: var(--gray-800) !important;
        border-top: 1px solid var(--gray-600) !important;
    }

    .nav-menu.active {
        display: block;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 0;
        padding: 0.5rem 0;
        margin: 0;
    }

    .nav-menu a {
        padding: 1rem 1.5rem;
        margin: 0;
        border-radius: 0;
        border-bottom: 1px solid var(--gray-100);
        font-size: 1rem;
        display: block;
        width: 100%;
        box-sizing: border-box;
        min-height: 48px; /* Better touch target */
        display: flex;
        align-items: center;
    }

    [data-theme="dark"] .nav-menu a {
        border-bottom: 1px solid var(--gray-600) !important;
        color: var(--gray-100) !important;
    }

    .nav-menu a:last-child {
        border-bottom: none;
    }

    .nav-menu a:hover,
    .nav-menu a.active {
        background: var(--primary-red);
        color: white;
        transform: none; /* Remove translateY for mobile */
    }

    .mobile-menu-toggle {
        display: block;
        padding: 0.75rem;
        min-height: 48px; /* Better touch target */
        min-width: 48px;
        border-radius: 8px;
        transition: all 0.3s ease;
        background: var(--gray-100);
        border: 1px solid var(--gray-200);
    }

    .mobile-menu-toggle:hover {
        background: var(--primary-red);
        color: white;
        border-color: var(--primary-red);
    }

    [data-theme="dark"] .mobile-menu-toggle {
        background: var(--gray-700) !important;
        border-color: var(--gray-600) !important;
        color: var(--gray-100) !important;
    }

    .header-controls {
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .lang-btn,
    .theme-btn {
        padding: 0.625rem 0.75rem;
        font-size: 0.85rem;
        gap: 0.25rem;
        min-height: 44px; /* Better touch target */
    }
}

/* Ensure body has top padding for fixed header */
body {
    padding-top: 80px;
}

/* Tablet adjustments */
@media (max-width: 1024px) {
    .nav-menu a {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .header-controls {
        gap: 0.375rem;
    }

    .lang-btn,
    .theme-btn {
        padding: 0.625rem 0.75rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 70px;
    }
}

/* Small mobile adjustments */
@media (max-width: 480px) {
    .header-content {
        padding: 0.75rem 0;
        gap: 0.5rem;
    }

    .logo {
        font-size: 1.125rem;
    }

    .header-controls {
        gap: 0.375rem;
    }

    .lang-btn,
    .theme-btn {
        padding: 0.5rem 0.625rem;
        font-size: 0.75rem;
        min-height: 40px;
        min-width: 40px;
    }

    .mobile-menu-toggle {
        padding: 0.625rem;
        min-height: 44px;
        min-width: 44px;
        font-size: 1.25rem;
    }

    .nav-menu a {
        padding: 1rem 1.25rem;
        font-size: 0.95rem;
        min-height: 48px;
    }
}

/* Accessibility improvements */
.mobile-menu-toggle {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.nav-menu a {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

/* Prevent horizontal scroll on mobile */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden;
    }

    .header {
        width: 100%;
        max-width: 100vw;
    }

    .header-content {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }
}

