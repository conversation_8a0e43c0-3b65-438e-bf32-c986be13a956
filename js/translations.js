// Translation System
const translations = {
    en: {
        // Brand
        brand: "BDC",
        
        // Navigation
        nav: {
            home: "Home",
            products: "Products",
            about: "About Us",
            contact: "Contact"
        },
        
        // Hero Section
        hero: {
            title: "Premium Cleaning Solutions for Every Need",
            subtitle: "Discover our complete range of professional-grade cleaning products designed to keep your home and business spotless",
            shop_now: "Shop Now",
            learn_more: "Learn More",
            products: "Products",
            categories: "Categories",
            effective: "Effective",
            years: "Years"
        },
        
        // Categories
        categories: {
            title: "Product Categories",
            subtitle: "Choose from our wide range of cleaning solutions",
            detergent_powder: "Detergent Powder",
            detergent_powder_desc: "Powerful cleaning for all fabrics",
            dishwashing: "Dishwashing Liquid",
            dishwashing_desc: "Gentle yet effective dish cleaning",
            floor_cleaner: "Floor Cleaner",
            floor_cleaner_desc: "Antibacterial floor cleaning solutions",
            detergent_liquid: "Detergent Liquid",
            detergent_liquid_desc: "Premium liquid detergents",
            glass_cleaner: "Glass Cleaner",
            glass_cleaner_desc: "Crystal clear glass cleaning",
            hand_washing: "Hand Washing",
            hand_washing_desc: "Gentle hand care products",
            bleach: "Bleach",
            bleach_desc: "Powerful stain removal",
            toilet_cleaner: "Toilet Cleaner",
            toilet_cleaner_desc: "Complete bathroom cleaning"
        },
        
        // Features
        features: {
            title: "Why Choose BDC?",
            quality: {
                title: "Premium Quality",
                desc: "All our products are made with the highest quality ingredients and undergo rigorous testing."
            },
            eco: {
                title: "Eco-Friendly",
                desc: "Environmentally conscious formulations that are safe for your family and the planet."
            },
            trusted: {
                title: "Trusted Brand",
                desc: "Over 20 years of experience serving millions of satisfied customers worldwide."
            },
            delivery: {
                title: "Fast Delivery",
                desc: "Quick and reliable delivery service to get your products when you need them."
            }
        },
        
        // CTA
        cta: {
            title: "Ready to Experience Premium Cleaning?",
            subtitle: "Browse our complete product range and find the perfect cleaning solution for your needs.",
            button: "View All Products"
        },
        
        // Products Page
        products: {
            hero: {
                title: "Our Premium Products",
                subtitle: "Discover our complete range of professional cleaning solutions"
            },
            categories: {
                title: "Categories",
                all: "All Products"
            },
            results: "products found",
            benefits: "Benefits",
            how_to_use: "How to Use",
            pricing: "Pricing",
            caution_storage: "Caution & Storage",
            caution: "Caution:",
            caution_text: "Avoid contact with eyes; keep out of reach of children.",
            storage: "Storage:",
            storage_text: "Close lid after use; store in a cool, dry place.",
            
            // Specific Products
            detergent_powder: {
                summer_flower: {
                    title: "Detergent Powder (Summer Flower)",
                    benefits: "Removes stains and odors on clothes, leaves a floral scent, gentle on hands; suitable for both machine and hand wash."
                },
                lemon: {
                    title: "Detergent Powder (Lemon)",
                    benefits: "Whitens fabrics, natural lemon scent, gentle on hands; suitable for both machine and hand wash."
                },
                pises: {
                    title: "Detergent Powder (PISES)",
                    benefits: "Dissolves easily, double stain-removal power, restores brightness; suitable for both machine and hand wash."
                },
                pises_sakura: {
                    title: "Detergent Powder (PISES Sakura)",
                    benefits: "Dissolves easily, double stain-removal power, restores brightness; suitable for both machine and hand wash."
                },
                super_clean: {
                    title: "Detergent Super Clean & Fresh Purple",
                    benefits: "Dissolves easily, double stain-removal power, restores brightness; suitable for both machine and hand wash."
                },
                usage: {
                    step1: "Separate white and colored clothes.",
                    step2: "Dissolve 30 g of powder in 3–4 L of water; stir to form foam and soak garments for up to 30 min.",
                    step3: "Rinse 2–3 times with clean water and sun-dry."
                }
            },
            
            dishwashing: {
                title: "Dishwashing Liquid",
                benefits: "Made from 100% natural lemon, double-strength oil removal, contains vitamin E to protect hands, refreshing lemon scent.",
                usage: "Mix 2 TBSP in 4–5 L of water; use a damp sponge to create soapy bubbles; wash dishes; rinse thoroughly."
            }
        },
        
        // About Page
        about: {
            hero: {
                title: "About BDC best quality in cambodia",
                subtitle: "Leading the way in premium cleaning solutions for over 20 years"
            },
            mission: {
                title: "Our Mission",
                content: "To provide the highest quality cleaning products that make life easier while protecting the environment and ensuring the safety of families worldwide."
            },
            vision: {
                title: "Our Vision",
                content: "To be the global leader in sustainable cleaning solutions, setting the standard for quality, innovation, and environmental responsibility."
            },
            values: {
                title: "Our Values",
                quality: "Quality Excellence",
                quality_desc: "We never compromise on the quality of our products or services.",
                innovation: "Innovation",
                innovation_desc: "Continuously developing new and better cleaning solutions.",
                sustainability: "Sustainability",
                sustainability_desc: "Committed to protecting our planet for future generations.",
                trust: "Trust",
                trust_desc: "Building lasting relationships through reliability and transparency."
            }
        },
        
        // Contact Page
        contact: {
            hero: {
                title: "Contact Us",
                subtitle: "Get in touch with our team for any questions or support"
            },
            form: {
                title: "Send us a Message",
                name: "Full Name",
                email: "Email Address",
                subject: "Subject",
                message: "Message",
                send: "Send Message"
            },
            info: {
                title: "Contact Information",
                address: "Our Address",
                phone: "Phone Number",
                email: "Email Address",
                fax: "Fax Number",
                hours: "Business Hours"
            }
        },
        
        // Footer
        footer: {
            about: {
                title: "About BDC",
                desc: "Leading provider of premium cleaning solutions for homes and businesses worldwide. Trusted by millions for over 20 years."
            },
            quick_links: {
                title: "Quick Links"
            },
            categories: {
                title: "Product Categories"
            },
            contact: {
                title: "Contact Info",
                phone: "(855) 89 982 999",
                email: "<EMAIL>",
                address: "S.I Building, #93, Preah Sihanouk Blvd, Phnom Penh"
            },
            copyright: "© 2024 BDC best quality in cambodia. All rights reserved. Professional cleaning solutions for every need."
        }
    },
    
    km: {
        // Brand
        brand: "BDC",
        
        // Navigation
        nav: {
            home: "ទំព័រដើម",
            products: "ផលិតផល",
            about: "អំពីយើង",
            contact: "ទំនាក់ទំនង"
        },
        
        // Hero Section
        hero: {
            title: "ដំណោះស្រាយសម្អាតកម្រិតខ្ពស់សម្រាប់គ្រប់ការត្រូវការ",
            subtitle: "ស្វែងយល់ពីផលិតផលសម្អាតកម្រិតវិជ្ជាជីវៈរបស់យើងដែលត្រូវបានរចនាឡើងដើម្បីរក្សាផ្ទះ និងអាជីវកម្មរបស់អ្នកឱ្យស្អាត",
            shop_now: "ទិញឥឡូវនេះ",
            learn_more: "ស្វែងយល់បន្ថែម",
            products: "ផលិតផល",
            categories: "ប្រភេទ",
            effective: "មានប្រសិទ្ធភាព",
            years: "ឆ្នាំ"
        },
        
        // Categories
        categories: {
            title: "ប្រភេទផលិតផល",
            subtitle: "ជ្រើសរើសពីដំណោះស្រាយសម្អាតដ៏ធំទូលាយរបស់យើង",
            detergent_powder: "ម្សៅសម្អាតខោអាវ",
            detergent_powder_desc: "សម្អាតដ៏មានអានុភាពសម្រាប់គ្រប់ប្រភេទក្រណាត់",
            dishwashing: "ទឹកសម្អាតចាន",
            dishwashing_desc: "សម្អាតចានដ៏ទន់ភ្លន់ប៉ុន្តែមានប្រសិទ្ធភាព",
            floor_cleaner: "ទឹកសម្អាតកម្រាល",
            floor_cleaner_desc: "ដំណោះស្រាយសម្អាតកម្រាលប្រឆាំងបាក់តេរី",
            detergent_liquid: "ទឹកសម្អាតខោអាវ",
            detergent_liquid_desc: "ទឹកសម្អាតកម្រិតខ្ពស់",
            glass_cleaner: "ទឹកសម្អាតកញ្ចក់",
            glass_cleaner_desc: "សម្អាតកញ្ចក់ឱ្យថ្លា",
            hand_washing: "សម្អាតដៃ",
            hand_washing_desc: "ផលិតផលថែរក្សាដៃដ៏ទន់ភ្លន់",
            bleach: "ទឹកកម្ចាត់ពណ៌",
            bleach_desc: "កម្ចាត់ស្នាមប្រឡាក់ដ៏មានអានុភាព",
            toilet_cleaner: "ទឹកសម្អាតបង្គន់",
            toilet_cleaner_desc: "សម្អាតបន្ទប់ទឹកពេញលេញ"
        },
        
        // Features
        features: {
            title: "ហេតុអ្វីបានជាជ្រើសរើស BDC?",
            quality: {
                title: "គុណភាពកម្រិតខ្ពស់",
                desc: "ផលិតផលទាំងអស់របស់យើងត្រូវបានផលិតដោយគ្រឿងផ្សំគុណភាពខ្ពស់បំផុត និងឆ្លងកាត់ការសាកល្បងយ៉ាងម៉ត់ចត់។"
            },
            eco: {
                title: "ស្រស់បរិស្ថាន",
                desc: "រូបមន្តដែលគិតគូរពីបរិស្ថានដែលមានសុវត្ថិភាពសម្រាប់គ្រួសារ និងភពផែនដីរបស់អ្នក។"
            },
            trusted: {
                title: "ម៉ាកដែលទុកចិត្តបាន",
                desc: "ជាងពីរទសវត្សរ៍នៃបទពិសោធន៍ក្នុងការបម្រើអតិថិជនដែលពេញចិត្តរាប់លាននាក់នៅទូទាំងពិភពលោក។"
            },
            delivery: {
                title: "ការដឹកជញ្ជូនលឿន",
                desc: "សេវាកម្មដឹកជញ្ជូនលឿន និងអាចទុកចិត្តបានដើម្បីទទួលបានផលិតផលរបស់អ្នកនៅពេលដែលអ្នកត្រូវការ។"
            }
        },
        
        // CTA
        cta: {
            title: "តើអ្នកត្រៀមខ្លួនរួចហើយឬនៅដើម្បីទទួលបានបទពិសោធន៍សម្អាតកម្រិតខ្ពស់?",
            subtitle: "រកមើលផលិតផលពេញលេញរបស់យើង និងស្វែងរកដំណោះស្រាយសម្អាតដ៏ល្អឥតខ្ចោះសម្រាប់ការត្រូវការរបស់អ្នក។",
            button: "មើលផលិតផលទាំងអស់"
        },
        
        // Products Page
        products: {
            hero: {
                title: "ផលិតផលកម្រិតខ្ពស់របស់យើង",
                subtitle: "ស្វែងយល់ពីដំណោះស្រាយសម្អាតវិជ្ជាជីវៈពេញលេញរបស់យើង"
            },
            categories: {
                title: "ប្រភេទ",
                all: "ផលិតផលទាំងអស់"
            },
            results: "ផលិតផលដែលរកឃើញ",
            benefits: "អត្ថប្រយោជន៍",
            how_to_use: "របៀបប្រើប្រាស់",
            pricing: "តម្លៃ",
            caution_storage: "ការប្រុងប្រយ័ត្ន និងការរក្សាទុក",
            caution: "ការប្រុងប្រយ័ត្ន៖",
            caution_text: "កុំឱ្យប៉ះពាល់នឹងភ្នែក; រក្សាឱ្យឆ្ងាយពីកុមារ។",
            storage: "ការរក្សាទុក៖",
            storage_text: "បិទគម្របបន្ទាប់ពីប្រើប្រាស់; រក្សាទុកនៅកន្លែងត្រជាក់ និងស្ងួត។",
            
            // Specific Products
            detergent_powder: {
                summer_flower: {
                    title: "ម្សៅសម្អាតខោអាវ (ផ្កាក្រអូបរដូវក្តៅ)",
                    benefits: "កម្ចាត់ស្នាមប្រឡាក់ និងក្លិនអាក្រក់លើខោអាវ, ទុកក្លិនផ្កាក្រអូប, ទន់ភ្លន់ចំពោះដៃ; សមស្របសម្រាប់ទាំងម៉ាស៊ីន និងលាងដោយដៃ។"
                },
                lemon: {
                    title: "ម្សៅសម្អាតខោអាវ (ក្រូចឆ្មា)",
                    benefits: "ធ្វើឱ្យក្រណាត់ស, ក្លិនក្រូចឆ្មាធម្មជាតិ, ទន់ភ្លន់ចំពោះដៃ; សមស្របសម្រាប់ទាំងម៉ាស៊ីន និងលាងដោយដៃ។"
                },
                pises: {
                    title: "ម្សៅសម្អាតខោអាវ (PISES)",
                    benefits: "រលាយបានយ៉ាងងាយស្រួល, អានុភាពកម្ចាត់ស្នាមប្រឡាក់ទ្វេដង, ស្តារភាពភ្លឺ; សមស្របសម្រាប់ទាំងម៉ាស៊ីន និងលាងដោយដៃ។"
                },
                pises_sakura: {
                    title: "ម្សៅសម្អាតខោអាវ (PISES Sakura)",
                    benefits: "រលាយបានយ៉ាងងាយស្រួល, អានុភាពកម្ចាត់ស្នាមប្រឡាក់ទ្វេដង, ស្តារភាពភ្លឺ; សមស្របសម្រាប់ទាំងម៉ាស៊ីន និងលាងដោយដៃ។"
                },
                super_clean: {
                    title: "ម្សៅសម្អាត Super Clean & Fresh Purple",
                    benefits: "រលាយបានយ៉ាងងាយស្រួល, អានុភាពកម្ចាត់ស្នាមប្រឡាក់ទ្វេដង, ស្តារភាពភ្លឺ; សមស្របសម្រាប់ទាំងម៉ាស៊ីន និងលាងដោយដៃ។"
                },
                usage: {
                    step1: "បំបែកខោអាវស និងពណ៌។",
                    step2: "រលាយម្សៅ 30 ក្រាមក្នុងទឹក 3-4 លីត្រ; កូរឱ្យកើតពពុះ និងជ្រលក់ខោអាវរហូតដល់ 30 នាទី។",
                    step3: "លាងជម្រះ 2-3 ដង ដោយទឹកស្អាត និងហាលថ្ងៃ។"
                }
            },
            
            dishwashing: {
                title: "ទឹកសម្អាតចាន",
                benefits: "ផលិតពីក្រូចឆ្មាធម្មជាតិ 100%, កម្ចាត់ប្រេងទ្វេដង, មានវីតាមីន E ដើម្បីការពារដៃ, ក្លិនក្រូចឆ្មាស្រស់។",
                usage: "លាយស្លាបព្រាបាយ 2 ក្នុងទឹក 4-5 លីត្រ; ប្រើអេប៉ុងសើមដើម្បីបង្កើតពពុះសាប៊ូ; លាងចាន; លាងជម្រះឱ្យស្អាត។"
            }
        },
        
        // About Page
        about: {
            hero: {
                title: "អំពី BDC គុណភាពល្អបំផុតនៅកម្ពុជា",
                subtitle: "ដឹកនាំផ្លូវក្នុងដំណោះស្រាយសម្អាតកម្រិតខ្ពស់អស់រយៈពេលជាង 20 ឆ្នាំ"
            },
            mission: {
                title: "បេសកកម្មរបស់យើង",
                content: "ផ្តល់ផលិតផលសម្អាតគុណភាពខ្ពស់បំផុតដែលធ្វើឱ្យជីវិតកាន់តែងាយស្រួល ខណៈពេលដែលការពារបរិស្ថាន និងធានាសុវត្ថិភាពគ្រួសារនៅទូទាំងពិភពលោក។"
            },
            vision: {
                title: "ចក្ខុវិស័យរបស់យើង",
                content: "ក្លាយជាអ្នកដឹកនាំសកលក្នុងដំណោះស្រាយសម្អាតប្រកបដោយចីរភាព, កំណត់ស្តង់ដារសម្រាប់គុណភាព, ការច្នៃប្រឌិត, និងការទទួលខុសត្រូវបរិស្ថាន។"
            },
            values: {
                title: "តម្លៃរបស់យើង",
                quality: "ឧត្តមភាពគុណភាព",
                quality_desc: "យើងមិនដែលសម្របសម្រួលលើគុណភាពផលិតផល ឬសេវាកម្មរបស់យើងទេ។",
                innovation: "ការច្នៃប្រឌិត",
                innovation_desc: "អភិវឌ្ឍដំណោះស្រាយសម្អាតថ្មី និងប្រសើរជាងមុនជាបន្តបន្ទាប់។",
                sustainability: "ចីរភាព",
                sustainability_desc: "ប្តេជ្ញាចិត្តក្នុងការការពារភពផែនដីរបស់យើងសម្រាប់មនុស្សជំនាន់ក្រោយ។",
                trust: "ទំនុកចិត្ត",
                trust_desc: "បង្កើតទំនាក់ទំនងយូរអង្វែងតាមរយៈភាពអាចទុកចិត្តបាន និងតម្លាភាព។"
            }
        },
        
        // Contact Page
        contact: {
            hero: {
                title: "ទំនាក់ទំនងយើង",
                subtitle: "ទាក់ទងមកកាន់ក្រុមការងាររបស់យើងសម្រាប់សំណួរ ឬការគាំទ្រណាមួយ"
            },
            form: {
                title: "ផ្ញើសារមកកាន់យើង",
                name: "ឈ្មោះពេញ",
                email: "អាសយដ្ឋានអ៊ីមែល",
                subject: "ប្រធានបទ",
                message: "សារ",
                send: "ផ្ញើសារ"
            },
            info: {
                title: "ព័ត៌មានទំនាក់ទំនង",
                address: "អាសយដ្ឋានរបស់យើង",
                phone: "លេខទូរស័ព្ទ",
                email: "អាសយដ្ឋានអ៊ីមែល",
                fax: "លេខទូរសារ",
                hours: "ម៉ោងធ្វើការ"
            }
        },
        
        // Footer
        footer: {
            about: {
                title: "អំពី BDC",
                desc: "អ្នកផ្តល់សេវាកម្មដឹកនាំនៃដំណោះស្រាយសម្អាតកម្រិតខ្ពស់សម្រាប់ផ្ទះ និងអាជីវកម្មនៅទូទាំងពិភពលោក។ ទទួលបានទំនុកចិត្តពីមនុស្សរាប់លាននាក់អស់រយៈពេលជាង 20 ឆ្នាំ។"
            },
            quick_links: {
                title: "តំណភ្ជាប់រហ័ស"
            },
            categories: {
                title: "ប្រភេទផលិតផល"
            },
            contact: {
                title: "ព័ត៌មានទំនាក់ទំនង",
                phone: "(855) 89 982 999",
                email: "<EMAIL>",
                address: "S.I Building, #93, Preah Sihanouk Blvd, Phnom Penh"
            },
            copyright: "© 2024 BDC គុណភាពល្អបំផុតនៅកម្ពុជា។ រក្សាសិទ្ធិទាំងអស់។ ដំណោះស្រាយសម្អាតវិជ្ជាជីវៈសម្រាប់គ្រប់ការត្រូវការ។"
        }
    }
};

// Cookie utility functions (if not already defined)
if (typeof setCookie === 'undefined') {
    function setCookie(name, value, days = 365) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    }
}

if (typeof getCookie === 'undefined') {
    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
}

// Language Management
class LanguageManager {
    constructor() {
        this.currentLanguage = getCookie('language') || localStorage.getItem('language') || 'en';
        this.init();
    }

    init() {
        this.applyTranslations();
        this.updateLanguageButton();
    }

    applyTranslations() {
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.placeholder = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });
    }

    getTranslation(key) {
        const keys = key.split('.');
        let translation = translations[this.currentLanguage];
        
        for (const k of keys) {
            if (translation && translation[k]) {
                translation = translation[k];
            } else {
                // Fallback to English if translation not found
                translation = translations.en;
                for (const fallbackKey of keys) {
                    if (translation && translation[fallbackKey]) {
                        translation = translation[fallbackKey];
                    } else {
                        return key; // Return key if no translation found
                    }
                }
                break;
            }
        }
        
        return translation;
    }

    setLanguage(language) {
        this.currentLanguage = language;
        // Save to both cookie and localStorage for compatibility
        setCookie('language', language);
        localStorage.setItem('language', language);
        this.applyTranslations();
        this.updateLanguageButton();

        // Update document language attribute
        document.documentElement.lang = language === 'km' ? 'km' : 'en';
    }

    updateLanguageButton() {
        const langButton = document.getElementById('current-lang');
        if (langButton) {
            langButton.textContent = this.currentLanguage === 'km' ? 'KM' : 'EN';
        }
    }

    toggle() {
        const newLanguage = this.currentLanguage === 'en' ? 'km' : 'en';
        this.setLanguage(newLanguage);
    }
}

// Initialize language manager
const languageManager = new LanguageManager();

// Global function for language toggle
function toggleLanguage() {
    languageManager.toggle();
}

// Initialize translations on page load
document.addEventListener('DOMContentLoaded', () => {
    languageManager.applyTranslations();
});

